package com.juzifenqi.plus.module.order.model.impl;

import com.juzifenqi.plus.module.order.model.IPlusMonthMemberRenewalPlanModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusMonthMemberRenewalPlanRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/19  11:22
 * @description
 */
@Slf4j
@Service
public class IPlusMonthMemberRenewalPlanModelImpl implements IPlusMonthMemberRenewalPlanModel {

    @Autowired
    private IPlusMonthMemberRenewalPlanRepository plusMonthMemberRenewalPlanRepository;

    @Override
    public Boolean cancelRenewalPlan(String orderSn) {
        return plusMonthMemberRenewalPlanRepository.cancelRenewalPlan(orderSn);
    }
}

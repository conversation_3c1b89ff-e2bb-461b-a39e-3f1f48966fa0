package com.juzifenqi.plus.module.order.model.impl;

import static com.juzifenqi.plus.constants.CommonConstant.RDZX_SUBJECT;
import static com.juzifenqi.plus.constants.CommonConstant.SUBJECT;
import static com.juzifenqi.plus.constants.PaySourceConstant.PAY_SOURCE_MEMBER;
import static com.juzifenqi.plus.constants.SerialNoPrefixConstant.SERIAL_NO_PREFIX_DFTK;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.RefundInfo;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.pay.BankAccountTypeEnum;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.enums.refund.RefundInfoStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IEncryptRepository;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IMemberPlusSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.converter.condtions.ShuntCondition;
import com.juzifenqi.plus.module.common.entity.DefrayPayResultEntity;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.event.DefrayApplyEvent;
import com.juzifenqi.plus.module.order.model.*;
import com.juzifenqi.plus.module.order.model.contract.IPastMemberRefundRecordRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PastMemberRefundRecordEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderShuntEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.defray.PayDefrayResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IMessageExternalRepository;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.converter.IPlusOrderDefrayModelConverter;
import com.juzifenqi.plus.module.order.model.event.order.*;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import com.juzifenqi.plus.utils.RedisLock;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.plus.utils.SerialNoUtils;
import com.juzifenqi.plus.utils.SwitchUtil;
import com.juzishuke.dss.common.enums.DataTypeEnum;
import com.juzishuke.framework.util.date.DateUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 订单代付model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:32
 */
@Slf4j
@Component
public class PlusOrderDefrayModelImpl implements IPlusOrderDefrayModel {

    private final IPlusOrderDefrayModelConverter converter = IPlusOrderDefrayModelConverter.instance;

    @Autowired
    private IPastMemberRefundRecordRepository repository;
    @Autowired
    private IMemberPlusSystemLogRepository    logRepository;
    @Autowired
    private RedisLock                         redisLock;
    @Autowired
    private PlusOrderQueryModel               plusOrderQueryModel;
    @Autowired
    private PlusOrderModel                    plusOrderModel;
    @Autowired
    private IFmsRepository                    fmsRepository;
    @Autowired
    private IMessageExternalRepository        messageExternalRepository;
    @Autowired
    private IPlusOrderBillModel               orderBillModel;
    @Autowired
    private IPlusOrderShuntQueryModel         shuntQueryModel;
    @Autowired
    private ConfigProperties                  configProperties;
    @Autowired
    private IPlusShuntRepository              shuntRepository;
    @Autowired
    private IOrderExternalRepository          orderExternalRepository;
    @Autowired
    private IIMRepository                     imRepository;
    @Autowired
    private IPlusOrderRefundInfoModel         orderRefundInfoModel;
    @Autowired
    private RedissonClient                    redissonClient;
    @Autowired
    private SwitchUtil                        switchUtil;
    @Autowired
    private ShuntCondition                    shuntCondition;
    @Autowired
    private RedisUtils                        redisUtils;
    @Autowired
    private IEncryptRepository                encryptRepository;
    @Autowired
    private PlusOrderSeparateModel plusOrderSeparateModel;
    @Autowired
    private IPlusMonthMemberRenewalPlanModel plusMonthMemberRenewalPlanModel;


    // 打款状态=失败
    private static final String PAY_STATUS_CLOS = "CLOS";
    // 打款状态=成功
    private static final String PAY_STATUS_FINI = "FINI";

    @Override
    public void createDefrayApply(CreateDefrayApplyEvent event, PlusOrderEntity plusOrder) {
        // 保存代付信息
        PastMemberRefundRecordEntity record = new PastMemberRefundRecordEntity();
        record.setOrderSn(event.getOrderSn());
        record.setDefraySerialNo(
                SerialNoUtils.generateApplySerialNo(SERIAL_NO_PREFIX_DFTK, event.getOrderSn()));
        record.setRefundRatio(event.getRatio());
        record.setCardId(event.getCardId());
        record.setCardNo(event.getCtCardNo());
        record.setCustomerName(event.getCustomerName());
        record.setCancelReason(event.getCancelReason());
        record.setOperatingId(event.getOptId());
        record.setOperatingName(event.getOptName());
        record.setRemark(event.getRemark());
        record.setBusinessType(event.getCancelType() == PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()
                ? DefrayPayBusinessTypeEnum.PAST.getCode()
                : DefrayPayBusinessTypeEnum.DEFRAY.getCode());
        repository.save(record);
        // 操作日志
        boolean expire = event.getCancelType() == PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue();
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(plusOrder.getUserId()).channelId(plusOrder.getChannelId())
                .programId(plusOrder.getProgramId()).orderSn(plusOrder.getOrderSn())
                .reason(expire ? CancelReasonEnum.CANCEL_REASON_31.getCode()
                        : CancelReasonEnum.CANCEL_REASON_32.getCode()).optId(event.getOptId())
                .optName(event.getOptName());
        logRepository.saveCancelLogByUser(plusLog, expire ? LogNodeEnum.LOG_NODE_PAST_REFUND_APPLY
                : LogNodeEnum.LOG_NODE_DE_CANCEL_APPLY);
    }

    @Override
    public void orderDefrayExecute() {
        String redisKey = RedisConstantPrefix.PAST_CANCEL_JOB_REFUND;
        try {
            log.info("会员代付任务执行开始");
            List<PastMemberRefundRecordEntity> toBeRefundList = repository.getToBeRefundList(
                    Arrays.asList(DefrayPayBusinessTypeEnum.PAST.getCode(),
                            DefrayPayBusinessTypeEnum.DEFRAY.getCode()));
            log.info("本次逻辑需要触发打款操作的订单数量:{}", toBeRefundList.size());
            if (CollectionUtils.isEmpty(toBeRefundList)) {
                return;
            }
            //加锁
            boolean lock = redisLock.lock(redisKey, "1", 60);
            if (!lock) {
                log.info("打款短时间job重复触发：redisKey：{}", redisKey);
                return;
            }
            //批量修改为处理中
            repository.batchProcessing(
                    toBeRefundList.stream().map(PastMemberRefundRecordEntity::getId)
                            .collect(Collectors.toList()));
            //打款
            toBeRefundList.forEach(this::sendDefrayPay);
        } catch (Exception e) {
            LogUtil.printLog("任务触发打款操作时出现未知异常：", e);
        } finally {
            //解锁
            redisLock.unLock(redisKey);
        }
    }

    @Override
    public void orderDefrayPayResult(PayDefrayResultEntity result) {
        String serialNumber = result.getSerialNumber();
        String orderId = result.getOrderId();
        log.info("代付打款结果回调处理开始：{}", serialNumber);
        // 查询代付打款记录
        PastMemberRefundRecordEntity record = repository.getBySerialNumber(serialNumber);
        if (Objects.isNull(record)) {
            log.info("代付打款结果回调处理-未查询到打款记录：{}", serialNumber);
            // 如果流水号查不到，则可能是发起打款后调支付超时/异常，导致没有更新流水号
            record = repository.getByOrderSn(orderId);
            if (Objects.isNull(record)) {
                log.info("代付打款结果回调处理-再次查询仍未查到打款记录：{}", orderId);
                return;
            }
            // 更新流水号
            record.setSerialNumber(serialNumber);
        }
        if (record.getBusinessType() == DefrayPayBusinessTypeEnum.PAST.getCode()
                || record.getBusinessType() == DefrayPayBusinessTypeEnum.DEFRAY.getCode()) {
            // 代付取消逻辑
            handleDefrayResult(result, record);
            return;
        }
        log.info("非取消会员订单代付打款结果回调：{}，{}", orderId, serialNumber);
    }

    @Override
    public boolean hasIngEnd(String orderSn) {
        return repository.hasIngEnd(orderSn);
    }

    @Override
    public void changeCardDefrayPayRefund(OrderRefundNotifyEntity entity) {
        String thirdPayNum = entity.getThirdPayNum();
        log.info("换卡代付退款处理开始,{}", thirdPayNum);
        // 校验代付信息
        PastMemberRefundRecordEntity refundRecord = repository.getByDefraySerialNo(thirdPayNum);
        PlusOrderRefundDetailEntity refundDetailEntity = orderRefundInfoModel.getDetailByRefundSerialNo(thirdPayNum);
        if (refundDetailEntity != null) {
            dealFirstPayDefrayRefund(refundDetailEntity,  entity);
            return;
        }
        if (refundRecord == null) {
            log.info("换卡代付退款处理,代付信息不存在,{}", thirdPayNum);
            imRepository.sendImMessage(
                    "换卡代付退款处理,代付信息不存在,退款业务流水号:" + thirdPayNum);
            return;
        }
        // 校验订单退款信息
        PlusOrderRefundInfoEntity refundInfo = orderRefundInfoModel.getByRefundSerialNo(
                thirdPayNum);
        if (refundInfo == null) {
            log.info("换卡代付退款处理,订单退款信息不存在,{}", thirdPayNum);
            imRepository.sendImMessage(
                    "换卡代付退款处理,订单退款信息不存在,退款业务流水号:" + thirdPayNum);
            return;
        }
        // 校验会员订单信息
        String plusOrderSn = refundRecord.getOrderSn();
        PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(plusOrderSn);
        if (plusOrderInfo == null) {
            log.info("换卡代付退款处理,未查到订单信息,{}", plusOrderSn);
            return;
        }
        Integer orderState = plusOrderInfo.getOrderState();
        if (orderState == PlusOrderStateEnum.CANCELED.getCode()) {
            log.info("换卡代付退款处理,订单状态已经取消,{}", thirdPayNum);
            PastMemberRefundRecordEntity record = new PastMemberRefundRecordEntity();
            record.setId(refundRecord.getId());
            record.setOptStatus(PastCancelStatusEnum.OPT_STATE_2.getStatus());
            repository.updateState(record);
            return;
        }
        // 更新订单退款信息
        String status = entity.getStatus();
        Integer refundState =
                RefundStateEnum.S.getCode().equals(status) ? RefundInfoStateEnum.SUCCESS.getCode()
                        : RefundInfoStateEnum.FAIL.getCode();
        PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
        updateRefundInfo.setId(refundInfo.getId());
        updateRefundInfo.setRefundState(refundState);
        updateRefundInfo.setPaySerialNo(entity.getSerialNumber());
        updateRefundInfo.setPayCallbackTime(new Date());
        orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);
        // 更新代付信息状态
        PastMemberRefundRecordEntity record = new PastMemberRefundRecordEntity();
        record.setId(refundRecord.getId());
        record.setSerialNumber(entity.getSerialNumber());
        if (RefundStateEnum.F.getCode().equals(status)) {
            record.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
            record.setPayFailMsg(StringUtils.isBlank(entity.getErrorMsg()) ? "支付系统未提供失败原因"
                    : entity.getErrorMsg());
            repository.updateState(record);
            // 失败,通知黑卡出账
            outcomeNotify(plusOrderSn, CommonConstant.TWO, null);
            return;
        }
        // 成功,置空失败原因
        record.setOptStatus(PastCancelStatusEnum.OPT_STATE_2.getStatus());
        record.setPayFailMsg("");
        repository.updateState(record);
        // 作废会员月卡续费计划
        if (plusOrderInfo.getConfigId() == JuziPlusEnum.HYYK_CARD.getCode()
                && plusOrderInfo.getMonthPeriod() == 1) {
            plusMonthMemberRenewalPlanModel.cancelRenewalPlan(plusOrderSn);
        }
        // 成功,通知黑卡出账
        outcomeNotify(plusOrderSn, CommonConstant.ONE, entity.getRefundAmount());
        // 退款成功,按用户id维度加锁,防止取消订单挪动会员周期出现错误
        handleRefundSuccess(refundRecord, plusOrderInfo, thirdPayNum);
    }

    @Override
    public void defrayPayRefundSecond(OrderRefundSecondEvent event) {
        PastMemberRefundRecordEntity pastMemberRefundRecordEntity = repository.getById(event.getPastRecordId());
        if (pastMemberRefundRecordEntity == null) {
            return;
        }
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(event.getOrderSn());
        PlusOrderRefundDetailEntity detailEntity = orderRefundInfoModel.getDetailById(event.getRefundDetailId());
        PlusOrderRefundDetailEntity updateDetailEntity = new PlusOrderRefundDetailEntity();
        updateDetailEntity.setId(detailEntity.getId());
        updateDetailEntity.setRefundState(RefundInfoStateEnum.DOING.getCode());
        orderRefundInfoModel.updateOrderRefundDetail(updateDetailEntity);
        String businessScene = shuntRepository.getBusinessScene(event.getOrderSn());
        OrderCancelRefundVO vo = buildOrderCancelRefundVO(plusOrderEntity, pastMemberRefundRecordEntity, detailEntity, businessScene);
        OrderCancelRefundResultVO result = orderExternalRepository.closeOrderRefund(vo);
        // 支付侧交易流水号
        String paySerialNo = result != null ? result.getSerialNumber() : null;
        if (StringUtils.isNotBlank(paySerialNo)) {
            orderRefundInfoModel.updateRefundDetailBySerialNo(detailEntity.getRefundSerialNo(), paySerialNo);
        }
    }


    private void dealFirstPayDefrayRefund(PlusOrderRefundDetailEntity refundDetailEntity,
                                          OrderRefundNotifyEntity entity) {
        String thirdPayNum = refundDetailEntity.getRefundSerialNo();
        PastMemberRefundRecordEntity refundRecord = repository.getByDefraySerialNo(thirdPayNum.substring(0, thirdPayNum.length() - 2));
        // 校验订单退款信息
        PlusOrderRefundInfoEntity refundInfo = orderRefundInfoModel.getById(refundDetailEntity.getRefundInfoId());
        if (refundInfo == null) {
            log.info("换卡代付退款处理,订单退款信息不存在,{}", thirdPayNum);
            imRepository.sendImMessage(
                    "换卡代付退款处理,订单退款信息不存在,退款业务流水号:" + thirdPayNum);
            return;
        }
        // 校验会员订单信息
        String plusOrderSn = refundInfo.getOrderSn();
        PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(plusOrderSn);
        if (plusOrderInfo == null) {
            log.info("换卡代付退款处理,未查到订单信息,{}", plusOrderSn);
            return;
        }
        // 更新订单退款信息
        String status = entity.getStatus();
        boolean refundSuccess = RefundStateEnum.S.getCode().equals(status);
        Integer refundState = refundSuccess? RefundInfoStateEnum.SUCCESS.getCode() : RefundInfoStateEnum.FAIL.getCode();
        PlusOrderRefundDetailEntity updateRefundDetail = new PlusOrderRefundDetailEntity();
        updateRefundDetail.setId(refundDetailEntity.getId());
        updateRefundDetail.setRefundState(refundState);
        updateRefundDetail.setPaySerialNo(entity.getSerialNumber());
        updateRefundDetail.setPayCallbackTime(new Date());
        orderRefundInfoModel.updateOrderRefundDetail(updateRefundDetail);
        PlusOrderSeparateEntity separateEntity = plusOrderSeparateModel.getById(refundDetailEntity.getSeparateId());
        //更新完查询是否还有待退款的退款明细
        List<PlusOrderRefundDetailEntity> detailList = orderRefundInfoModel.getDetailsByInfoId(refundInfo.getId());
        boolean allSuccess = false;
        if (refundSuccess) {
            // 成功,出账处理
            outcomeNotifyNew(plusOrderSn, CommonConstant.ONE, entity.getRefundAmount(), separateEntity.getPaySerialNo());
            if (refundDetailEntity.getCurrentPeriod() < refundDetailEntity.getTotalPeriod()) {
                //发起下一次退款
                PlusOrderRefundDetailEntity nextRefundDetail = detailList.stream().filter(
                        t -> Objects.equals(t.getRefundState(),0) && Objects.equals(t.getCurrentPeriod(),2)).findFirst().orElse(null);
                if (nextRefundDetail != null) {
                    //异步发起新的代付请求
                    OrderRefundSecondEvent orderRefundSecondEvent = new OrderRefundSecondEvent();
                    orderRefundSecondEvent.setRefundType(PayRefundTypeEnum.CHANGE_CARD.getCode());
                    orderRefundSecondEvent.setRefundDetailId(nextRefundDetail.getId());
                    orderRefundSecondEvent.setOrderSn(plusOrderSn);
                    orderRefundSecondEvent.setPastRecordId(refundRecord.getId());
                    messageExternalRepository.sendSecondRefundMq(orderRefundSecondEvent);
                }
            } else {
                PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
                updateRefundInfo.setId(refundInfo.getId());
                updateRefundInfo.setRefundState(refundState);
                updateRefundInfo.setPayCallbackTime(new Date());
                orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);
                // 成功,置空失败原因
                refundRecord.setOptStatus(PastCancelStatusEnum.OPT_STATE_2.getStatus());
                refundRecord.setPayFailMsg("");
                repository.updateState(refundRecord);
                allSuccess = true;
            }
        } else {
            //退款失败
            outcomeNotifyNew(plusOrderSn, CommonConstant.TWO, null, separateEntity.getPaySerialNo());
            if (refundDetailEntity.getCurrentPeriod() < refundDetailEntity.getTotalPeriod()) {
                //更新另一笔记录为失败
                PlusOrderRefundDetailEntity nextRefundDetail = detailList.stream().filter(
                        t -> Objects.equals(t.getRefundState(),0) && Objects.equals(t.getCurrentPeriod(),2)).findFirst().orElse(null);
                if (nextRefundDetail != null) {
                    //更新记录为失败
                    PlusOrderRefundDetailEntity updateDetail = new PlusOrderRefundDetailEntity();
                    updateDetail.setId(nextRefundDetail.getId());
                    updateDetail.setRefundState(RefundInfoStateEnum.FAIL.getCode());
                    updateDetail.setRemark("首笔失败");
                    orderRefundInfoModel.updateOrderRefundDetail(updateDetail);
                }
                PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
                updateRefundInfo.setId(refundInfo.getId());
                updateRefundInfo.setRefundState(refundState);
                updateRefundInfo.setPayCallbackTime(new Date());
                orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);
                // 成功,置空失败原因
                refundRecord.setOptStatus(PastCancelStatusEnum.OPT_STATE_2.getStatus());
                refundRecord.setPayFailMsg("");
                repository.updateState(refundRecord);
            } else {
                //第二笔失败，先做报警处理，定时任务手动补偿
                log.error("orderSn:{}, 代付退款第二笔失败, detailId:{}", plusOrderSn, refundDetailEntity.getId());
                imRepository.sendImMessage(String.format("orderSn:%s, 第二笔退款失败", plusOrderSn));
            }
        }
        if (allSuccess) {
            // 退款成功,发送mq取消订单
            handleRefundSuccess(refundRecord, plusOrderInfo, thirdPayNum);
        }
    }

    @Override
    public void initPastCardNoUuid(Long index, Integer size) {
        long lastId = index;
        if (redisUtils.hasKey(RedisConstantPrefix.PAST_MEMBER_REFUND_RECORD_BEGIN_ID)) {
            lastId = Long.parseLong(redisUtils.get(RedisConstantPrefix.PAST_MEMBER_REFUND_RECORD_BEGIN_ID));
        }
        log.info("代付打款任务跑批发起新银行卡密文初始化开始入参,last:{},size:{}", lastId, size);
        List<PastMemberRefundRecordEntity> refundRecordEntities = repository.getWithEmptyBankAccountNoUuid(lastId, size);
        if (CollectionUtils.isEmpty(refundRecordEntities)) {
            log.info("代付打款任务跑批发起新银行卡密文初始化，无待初始化代付打款任务返回");
            return;
        }
        refundRecordEntities.stream()
                .filter(item -> !StringUtils.contains(item.getCardNo(), "="))
                .forEach(record -> {
                    String encrypt = encryptRepository.encrypt(record.getCardNo(), "代付打款任务老加密");
                    record.setCardNo(encrypt);
                });
        List<String> content = refundRecordEntities.stream().map(PastMemberRefundRecordEntity::getCardNo).collect(Collectors.toList());
        Map<String, String> toUuidMap = shuntCondition.desToUuidBatch(content, DataTypeEnum.BANK_CARD);
        if (toUuidMap.isEmpty()){
            return;
        }
        for (PastMemberRefundRecordEntity pastMemberRefundRecordEntity : refundRecordEntities) {
            if (toUuidMap.containsKey(pastMemberRefundRecordEntity.getCardNo())) {
                pastMemberRefundRecordEntity.setCardNo(pastMemberRefundRecordEntity.getCardNo());
                pastMemberRefundRecordEntity.setCardNoUuid(toUuidMap.get(pastMemberRefundRecordEntity.getCardNo()));
            }
        }
        repository.updateBatchCardNoUuid(refundRecordEntities);
        // 更新Redis中的lastId为最后一条记录的ID
        PastMemberRefundRecordEntity lastRecord = refundRecordEntities.get(refundRecordEntities.size() - 1);
        redisUtils.setEx(RedisConstantPrefix.PAST_MEMBER_REFUND_RECORD_BEGIN_ID, String.valueOf(lastRecord.getId()), 1, TimeUnit.HOURS);
        log.info("代付打款任务跑批发起新银行卡密文初始化，结束:{}", size);
    }

    @Override
    public void inspectPastCardNoUuid(Integer size) {
        long lastId = 0L;
        Map<String, String> noEncryptCardNoMap = new HashMap<>();
        if (redisUtils.hasKey(RedisConstantPrefix.PAST_MEMBER_REFUND_RECORD_INSPECT_BEGIN_ID)) {
            lastId = Long.parseLong(redisUtils.get(RedisConstantPrefix.PAST_MEMBER_REFUND_RECORD_INSPECT_BEGIN_ID));
        }
        log.info("代付打款任务跑批发起新银行卡密文巡检开始入参,last:{},size:{}", lastId, size);
        List<PastMemberRefundRecordEntity> refundRecordEntities = repository.getWithCardNoUuid(lastId, size);
        if (CollectionUtils.isEmpty(refundRecordEntities)) {
            log.info("代付打款任务跑批发起新银行卡密文巡检，无待初始化代付打款任务返回");
            return;
        }
        List<String> encryptCardNoList = refundRecordEntities.stream()
                .map(PastMemberRefundRecordEntity::getCardNo)
                .filter(cardNo -> StringUtils.contains(cardNo, "=")).collect(Collectors.toList());
        Map<String, String> toUuidMap = shuntCondition.desToUuidBatch(encryptCardNoList, DataTypeEnum.BANK_CARD);
        if (refundRecordEntities.size() != encryptCardNoList.size()) {
            List<String> noEncryptCardNo = refundRecordEntities.stream()
                    .map(PastMemberRefundRecordEntity::getCardNo)
                    .filter(cardNo -> !StringUtils.contains(cardNo, "=")).collect(Collectors.toList());
            noEncryptCardNoMap = encryptRepository.encryptBankCardBatch(noEncryptCardNo, "新银行卡密文巡检");
        }
        for (PastMemberRefundRecordEntity pastMemberRefundRecordEntity : refundRecordEntities) {
            checkAndLogCardUuidMismatch(pastMemberRefundRecordEntity, toUuidMap);
            checkAndLogCardUuidMismatch(pastMemberRefundRecordEntity, noEncryptCardNoMap);
        }
        // 更新Redis中的lastId为最后一条记录的ID
        PastMemberRefundRecordEntity lastRecord = refundRecordEntities.get(refundRecordEntities.size() - 1);
        redisUtils.setEx(RedisConstantPrefix.PAST_MEMBER_REFUND_RECORD_INSPECT_BEGIN_ID, String.valueOf(lastRecord.getId()), 1, TimeUnit.HOURS);
        log.info("代付打款任务跑批发起新银行卡密文巡检，结束:{}", size);
    }

    private void checkAndLogCardUuidMismatch(PastMemberRefundRecordEntity record, Map<String, String> map) {
        if (!CollectionUtils.isEmpty(map) && map.containsKey(record.getCardNo())) {
            String expectedUuid = map.get(record.getCardNo());
            String actualUuid = record.getCardNoUuid();
            if (!expectedUuid.equals(actualUuid)) {
                log.info("代付打款任务跑批发起新银行卡密文巡检校验错误,id:{},cardNoUuid:{},expectedUuid:{}",
                        record.getId(), actualUuid, expectedUuid);
            }
        }
    }
    /**
     * 处理退款成功
     */
    private void handleRefundSuccess(PastMemberRefundRecordEntity record,
            PlusOrderEntity plusOrderInfo, String thirdPayNum) {
        String lockKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.PLUS_CANCEL_ORDER,
                plusOrderInfo.getUserId());
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            boolean result = rLock.tryLock(3, TimeUnit.SECONDS);
            if (!result) {
                throw new RuntimeException("处理订单中心退款通知,加锁失败");
            }
            // 取消订单
            sendDefraySynResult(record, plusOrderInfo, record.getPayAmount());
        } catch (Exception e) {
            log.info("处理订单中心退款通知异常", e);
            if (!(e instanceof PlusAbyssException)) {
                imRepository.sendImMessage(
                        "处理订单中心退款通知异常,用户id:" + plusOrderInfo.getUserId() + ",退款业务流水号:"
                                + thirdPayNum);
            }
            throw new RuntimeException(e);
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 处理回调
     */
    private void handleDefrayResult(PayDefrayResultEntity result,
            PastMemberRefundRecordEntity record) {
        String orderSn = record.getOrderSn();
        //订单是否存在
        PlusOrderEntity orderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
        if (Objects.isNull(orderInfo)) {
            log.info("代付打款结果回调处理-当前订单不存在：{}", orderSn);
            return;
        }
        //更新记录状态：
        PastMemberRefundRecordEntity saveRes = new PastMemberRefundRecordEntity();
        saveRes.setId(record.getId());
        saveRes.setVerifyResult(result.getMsgContent());
        // 更新流水号
        saveRes.setSerialNumber(record.getSerialNumber());
        //判断打款状态
        if (!PAY_STATUS_FINI.equals(result.getTradeStatus())) {
            log.info("代付打款结果回调处理-打款失败：{}", orderSn);
            //打款失败：3_退款失败
            saveRes.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
            saveRes.setPayFailMsg(StringUtils.isNotBlank(result.getTradeMsg()) ? "支付系统未提供失败原因"
                    : result.getTradeMsg());
            repository.updateState(saveRes);
            // 20230804 zjf 打款失败，处理出账数据
            outcomeNotify(orderSn, CommonConstant.TWO, null);
            return;
        }
        log.info("代付打款结果回调处理-打款成功：{}", orderSn);
        // 打款成功、置空失败原因
        saveRes.setOptStatus(PastCancelStatusEnum.OPT_STATE_2.getStatus());
        saveRes.setPayFailMsg("");
        repository.updateState(saveRes);
        // 打款成功处理出账
        outcomeNotify(orderSn, CommonConstant.ONE, result.getTradeAmount());
        // 取消会员订单
        sendDefraySynResult(record, orderInfo, result.getTradeAmount());
    }

    /**
     * 发起代付请求
     */
    private void sendDefrayPay(PastMemberRefundRecordEntity record) {
        String orderSn = record.getOrderSn();
        log.info("发起代付请求开始：{}", orderSn);
        try {
            //订单是否存在
            PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
            if (Objects.isNull(plusOrderInfo)
                    || ( !Objects.equals(plusOrderInfo.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())
                    && plusOrderInfo.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode())) {
                throw new PlusAbyssException("发起代付请求,当前订单状态非支付成功");
            }
            if (Objects.equals(plusOrderInfo.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                if (plusOrderInfo.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode()
                        && plusOrderInfo.getOrderState() != PlusOrderStateEnum.WAIT_PAY.getCode()) {
                    throw new PlusAbyssException("发起代付请求,当前订单状态异常");
                }
                BigDecimal payAmount = plusOrderInfo.getPayAmount();
                if (payAmount == null || payAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new PlusAbyssException("发起代付请求,当前订单状态异常");
                }
            }
            // 非白名单用户走老逻辑代付
            if (!switchUtil.isNew(plusOrderInfo.getUserId())) {
                log.info("非白名单用户走老逻辑代付：{}", record.getOrderSn());
                doDefrayPay(record);
                return;
            }
            BigDecimal refundRatio = record.getRefundRatio();
            // 计算订单退款金额
            PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(record,
                    plusOrderInfo);
            cancelEvent.setCancelType(
                    record.getBusinessType() == DefrayPayBusinessTypeEnum.PAST.getCode()
                            ? PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()
                            : PlusCancelTypeEnum.NO_EXPIRE_PAYMENT.getValue());
            PlusOrderCancelEntity cancelEntity = plusOrderModel.calOrderRefundAmount(plusOrderInfo,
                    cancelEvent);
            if (!cancelEntity.isCanBeCancel()) {
                throw new PlusAbyssException(cancelEntity.getReason());
            }
            // 可退金额
            BigDecimal orderAmount = cancelEntity.getMoneyBack();
            // 处理打款金额（元）
            BigDecimal refundAmount = orderAmount.multiply(refundRatio)
                    .setScale(2, RoundingMode.HALF_DOWN);
            record.setPayAmount(refundAmount);
            log.info("发起打款操作-退款金额为：tradeAmount:{}元,orderSn：{}", refundAmount, orderSn);
            // 获取分流信息
            PlusOrderShuntEntity orderShunt = getOrderShunt(orderSn, plusOrderInfo.getConfigId());
            String outSupplier = getOutSupplier(orderShunt);
            if (refundAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.ZERO) {
                // 0元退款处理
                handleZeroOrder(record, plusOrderInfo, outSupplier);
                return;
            }
            String paySerialNo;
            try {
                // 取消类型
                Integer businessType = record.getBusinessType();
                Integer cancelType = businessType != null
                        && businessType == DefrayPayBusinessTypeEnum.PAST.getCode()
                        ? PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()
                        : PlusCancelTypeEnum.NO_EXPIRE_PAYMENT.getValue();
                // 还款卡或会员订单创建时间在配置时间之前退款,走纯代付请求
                Date orderCreateTime = plusOrderInfo.getCreateTime();
                boolean isBefore = orderCreateTime != null && DateUtil.compareDate(
                        DateUtil.parseDateTime(configProperties.cDefrayRefundOrderCreateTime),
                        orderCreateTime) >= 0;
                boolean cDefrayPay =
                        JuziPlusEnum.REPAY_CARD.getCode() == plusOrderInfo.getConfigId()
                                || isBefore;
                if (Objects.equals(plusOrderInfo.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())
                        && Objects.equals(plusOrderInfo.getOrderState(), PlusOrderStateEnum.PAY_SUCCESS.getCode())) {
                    //走新的代付取消逻辑
                    sendFirstPayDefrayPay(plusOrderInfo, record, cancelType,refundAmount);
                    return ;
                }
                // 保存退款信息（开启新事物提交，防止回调过来时还没有提交事务）
                PlusOrderRefundInfoEntity orderRefundInfo = converter.toPlusOrderRefundInfoEntity(
                        record, RefundInfoStateEnum.DOING.getCode(),
                        cDefrayPay ? RefundTypeEnum.CDF.getCode() : RefundTypeEnum.DFTK.getCode(),
                        false, true, cancelType);
                orderRefundInfoModel.saveOrderRefundInfo(orderRefundInfo);
                // 打款摘要处理
                String tradeSubject =
                        JuziPlusEnum.RDZX_CARD.getCode() == plusOrderInfo.getConfigId()
                                ? RDZX_SUBJECT : SUBJECT;
                // 业务场景
                String businessScene = shuntRepository.getBusinessScene(orderSn);
                //现在订单退款代付走订单（下面只过else的逻辑）
                if (cDefrayPay) {
                    // 走纯代付请求,调支付
                    DefrayApplyEvent event = converter.toDefrayApplyEvent(record, businessScene,
                            BankAccountTypeEnum.TO_PRIVATE.getType(), PAY_SOURCE_MEMBER,
                            tradeSubject, plusOrderInfo.getChannelId());
                    DefrayPayResultEntity result = fmsRepository.cDefrayPay(event);
                    if (result == null) {
                        throw new PlusAbyssException("纯代付请求支付异常");
                    }
                    // 支付侧交易流水号
                    paySerialNo = result.getSerialNumber();
                    record.setOptResult(result.getResultContent());
                } else {
                    // 走代付退款请求,调订单中心
                    // 是否部分退款
                    int partRefund = refundAmount.compareTo(BigDecimal.ZERO) > 0
                            && refundAmount.compareTo(orderAmount) < 0 ? 1 : 0;
                    OrderCancelRefundVO vo = converter.toOrderCancelRefundVO(record,
                            PayRefundTypeEnum.CHANGE_CARD.getCode(),
                            String.valueOf(plusOrderInfo.getChannelId()), PAY_SOURCE_MEMBER,
                            PayProductCodeEnum.TK.getCode(), businessScene, tradeSubject,
                            partRefund);
                    OrderCancelRefundResultVO result = orderExternalRepository.closeOrderRefund(vo);
                    if (result == null) {
                        throw new PlusAbyssException("代付退款,请求订单中心取消订单异常");
                    }
                    // 支付侧交易流水号
                    paySerialNo = result.getSerialNumber();
                    record.setOptResult(JSONObject.toJSONString(result));
                }
            } catch (Exception e) {
                LogUtil.printLog(e, "发起代付请求-出现异常 orderSn:" + orderSn);
                //调用未知异常：到此结束 3_退款失败
                record.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
                record.setOptResult("调用新支付系统发起代付请求超时，请二十分钟后再重试");
                record.setPayFailMsg(record.getOptResult());
                repository.updateState(record);
                // 退款失败
                orderRefundInfoModel.updateRefundState(record.getDefraySerialNo(),
                        RefundInfoStateEnum.FAIL.getCode());
                // 20230309 zjf 加锁
                tryLock(record, 1200L);
                return;
            }
            log.info("发起代付请求结束：{}，{}", JSON.toJSONString(record), orderSn);
            // 修改状态：处理中
            record.setSerialNumber(paySerialNo);
            record.setOptStatus(PastCancelStatusEnum.OPT_STATE_1.getStatus());
            repository.updateState(record);
            // 修改订单退款信息支付流水号
            orderRefundInfoModel.updatePaySerialNo(record.getDefraySerialNo(), paySerialNo);
            // 记录日志
            String cancelRemark = "系统开始发起打款,取消比例为" + record.getRefundRatio()
                    .multiply(new BigDecimal(100)) + "%;";
            if (StringUtils.isNotBlank(record.getRemark())) {
                cancelRemark += record.getRemark();
            }
            // 保存操作日志
            saveLog(record, plusOrderInfo, cancelRemark + ";该笔退款由" + outSupplier + "出账");
        } catch (Exception e) {
            LogUtil.printLog(e, "发起代付请求结束出现未知异常,订单号=" + orderSn);
            record.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
            record.setOptResult(e instanceof PlusAbyssException ? e.getMessage() : "系统处理异常");
            record.setPayFailMsg(record.getOptResult());
            repository.updateState(record);
        }
    }


    private void sendFirstPayDefrayPay (PlusOrderEntity order, PastMemberRefundRecordEntity record, Integer cancelType,BigDecimal refundAmount) {
        PlusOrderRefundInfoEntity orderRefundInfo = converter.toPlusOrderRefundInfoEntity(
                record, RefundInfoStateEnum.DOING.getCode(), RefundTypeEnum.DFTK.getCode(),
                false, true, cancelType);
        //查询支付明细
        List<PlusOrderSeparateEntity> plusOrderSeparateList = plusOrderSeparateModel.getPlusOrderSeparate(order.getOrderSn());
        List<PlusOrderSeparateEntity> successRecord = plusOrderSeparateList.stream().filter(
                        t -> Objects.equals(t.getSeparateState(), SeparateStateEnum.SUCCESS.getCode()))
                .sorted(Comparator.comparing(PlusOrderSeparateEntity::getTotalSeparateAmount).reversed())
                .collect(Collectors.toList());
        if (successRecord.size() != 2) {
            return;
        }
        BigDecimal balanceRefundAmt =  refundAmount;
        List<PlusOrderRefundDetailEntity> detailEntities = Lists.newArrayList();
        int index = 1;
        for (PlusOrderSeparateEntity separateEntity : successRecord) {
            if (balanceRefundAmt.compareTo(BigDecimal.ZERO) > 0) {
                PlusOrderRefundDetailEntity detailEntity = new PlusOrderRefundDetailEntity();
                detailEntity.setSeparateId(separateEntity.getId());
                detailEntity.setCurrentPeriod(index);
                detailEntity.setRefundSerialNo(orderRefundInfo.getRefundSerialNo() + "P" + index);
                BigDecimal detailRefundAmt = balanceRefundAmt.compareTo(separateEntity.getTotalSeparateAmount()) > 0
                        ? separateEntity.getTotalSeparateAmount() : balanceRefundAmt;
                detailEntity.setRefundAmount(detailRefundAmt);
                detailEntity.setTotalRefundAmount(refundAmount);
                detailEntity.setRefundState(index == 1 ? RefundInfoStateEnum.DOING.getCode() : RefundInfoStateEnum.INIT.getCode());
                balanceRefundAmt = balanceRefundAmt.subtract(detailRefundAmt);
                detailEntities.add(detailEntity);
                index++;
            }
        }
        // 保存退款信息（开启新事物提交，防止回调过来时还没有提交事务）
        orderRefundInfoModel.saveOrderRefundInfoAndDetail(orderRefundInfo, detailEntities);
        // 业务场景
        PlusOrderRefundDetailEntity orderRefundDetailEntity = detailEntities.get(0);
        String businessScene = shuntRepository.getBusinessScene(record.getOrderSn());
        OrderCancelRefundVO vo = buildOrderCancelRefundVO(order, record, orderRefundDetailEntity, businessScene);
        OrderCancelRefundResultVO result = orderExternalRepository.closeOrderRefund(vo);
        // 支付侧交易流水号
        String paySerialNo = result != null ? result.getSerialNumber() : null;
        orderRefundDetailEntity.setPaySerialNo(paySerialNo);
        // 代付退款记录更新为处理中
        record.setOptStatus(PastCancelStatusEnum.OPT_STATE_1.getStatus());
        record.setOptResult(JSONObject.toJSONString(result));
        repository.updateState(record);
    }

    private OrderCancelRefundVO buildOrderCancelRefundVO (PlusOrderEntity order, PastMemberRefundRecordEntity record,
                                                          PlusOrderRefundDetailEntity detailEntity,String businessScene) {
        OrderCancelRefundVO vo = new OrderCancelRefundVO();
        vo.setOrderSn(record.getOrderSn());
        vo.setOperatingId(record.getOperatingId());
        vo.setOperatingName(record.getOperatingName());
        vo.setCancelReason(String.valueOf(record.getCancelReason()));

        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setPartRefund(1);
        refundInfo.setRefundType(PayRefundTypeEnum.CHANGE_CARD.getCode());
        refundInfo.setApplication(String.valueOf(order.getChannelId()));
        refundInfo.setSource(PAY_SOURCE_MEMBER);
        refundInfo.setRefundAmount(detailEntity.getRefundAmount());
        refundInfo.setThirdPayNum(detailEntity.getRefundSerialNo());
        refundInfo.setPayProductCode(PayProductCodeEnum.TK.getCode());
        refundInfo.setBusinessScene(businessScene);
        refundInfo.setBankCardId(String.valueOf(record.getCardId()));
        refundInfo.setTradeSubject(SUBJECT);

        PlusOrderSeparateEntity separateEntity = plusOrderSeparateModel.getById(detailEntity.getSeparateId());
        String payNum = Objects.equals(separateEntity.getOrderPayAction(), OrderPayActionEnum.PAY_ACTION_1.getCode()) ?
                separateEntity.getSerialNo() : separateEntity.getPaySerialNo();
        refundInfo.setPayNum(payNum);
        refundInfo.setCancelPeriod(detailEntity.getTotalPeriod());
        refundInfo.setRefundTotalAmount(detailEntity.getRefundAmount());

        vo.setRefundInfo(refundInfo);
        return vo;
    }


    /**
     * 发起打款操作
     */
    private void doDefrayPay(PastMemberRefundRecordEntity record) {
        String orderSn = record.getOrderSn();
        log.info("订单开始代付打款：{}", orderSn);
        try {
            //订单是否存在
            PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
            if (Objects.isNull(plusOrderInfo)
                    || plusOrderInfo.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
                throw new PlusAbyssException("当前订单状态非支付成功");
            }
            BigDecimal refundRatio = record.getRefundRatio();
            // 计算订单退款金额
            PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(record,
                    plusOrderInfo);
            cancelEvent.setCancelType(
                    record.getBusinessType() == DefrayPayBusinessTypeEnum.PAST.getCode()
                            ? PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()
                            : PlusCancelTypeEnum.NO_EXPIRE_PAYMENT.getValue());
            PlusOrderCancelEntity cancelEntity = plusOrderModel.calOrderRefundAmount(plusOrderInfo,
                    cancelEvent);
            if (!cancelEntity.isCanBeCancel()) {
                throw new PlusAbyssException(cancelEntity.getReason());
            }
            // 可退金额
            BigDecimal orderAmount = cancelEntity.getMoneyBack();
            // 处理打款金额（元）
            BigDecimal refundAmount = orderAmount.multiply(refundRatio);
            record.setPayAmount(refundAmount);
            // 打款金额（分）
            BigDecimal tradeAmount = refundAmount.multiply(new BigDecimal(100));
            // 获取分流信息
            PlusOrderShuntEntity orderShunt = getOrderShunt(orderSn, plusOrderInfo.getConfigId());
            String outSupplier = getOutSupplier(orderShunt);
            log.info("发起打款操作-退款金额为：tradeAmount:{}分,orderSn：{}", tradeAmount, orderSn);
            if (tradeAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.ZERO) {
                // 0元退款处理
                handleZeroOrder(record, plusOrderInfo, outSupplier);
                return;
            }
            // 20240705 zjf 诺壹分流：诺壹传商编
            String defrayNo = getDefrayNo(orderShunt);
            // 打款标题处理
            String tradeName =
                    JuziPlusEnum.RDZX_CARD.getCode() == plusOrderInfo.getConfigId() ? RDZX_SUBJECT
                            : SUBJECT;
            try {
                // 调用支付打款接口
                DefrayPayResultEntity result = fmsRepository.doDefrayPay(tradeAmount, tradeName,
                        orderSn, record.getCardNo(), record.getCustomerName(), defrayNo);
                record.setOptResult(result.getResultContent());
                record.setSerialNumber(result.getSerialNumber());
                // 20230309 zjf 打款成功=终态
                if (PAY_STATUS_FINI.equals(result.getTradeStatus())) {
                    record.setOptStatus(PastCancelStatusEnum.OPT_STATE_2.getStatus());
                    // 发送消息,内部消费处理
                    sendDefraySynResult(record, plusOrderInfo, refundAmount);
                    // 打款成功，三方出账
                    outcomeNotify(orderSn, CommonConstant.ONE, refundAmount);
                } else if (PAY_STATUS_CLOS.equals(result.getTradeStatus())) {
                    // 打款失败=终态
                    record.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
                    record.setPayFailMsg(StringUtils.isBlank(result.getFailReason()) ? "支付系统未提供失败原因"
                            : result.getFailReason());
                    // 打款失败，三方出账
                    outcomeNotify(orderSn, CommonConstant.TWO, null);
                }
            } catch (Exception e) {
                LogUtil.printLog(e, "发起打款操作-出现异常 orderSn:" + orderSn);
                //调用未知异常：到此结束 3_退款失败
                record.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
                record.setOptResult("调用支付系统打款超时，请十分钟后再重试");
                record.setPayFailMsg(record.getOptResult());
                repository.updateState(record);
                // 20230309 zjf 加锁 10分钟
                tryLock(record, 600L);
                return;
            }
            log.info("发起打款操作-代付调用结束：{}，{}", JSON.toJSONString(record), orderSn);
            // 修改状态：处理中
            record.setOptStatus(PastCancelStatusEnum.OPT_STATE_1.getStatus());
            repository.updateState(record);
            // 记录日志
            String cancelRemark = "系统开始发起打款,取消比例为" + record.getRefundRatio()
                    .multiply(new BigDecimal(100)) + "%;";
            if (StringUtils.isNotBlank(record.getRemark())) {
                cancelRemark += record.getRemark();
            }
            // 保存操作日志
            saveLog(record, plusOrderInfo, cancelRemark + ";该笔退款由" + outSupplier + "出账");
        } catch (Exception e) {
            LogUtil.printLog(e, "发起打款操作-出现未知异常,订单号=" + orderSn);
            record.setOptStatus(PastCancelStatusEnum.OPT_STATE_3.getStatus());
            record.setOptResult(e instanceof PlusAbyssException ? e.getMessage() : "系统处理异常");
            record.setPayFailMsg(record.getOptResult());
            repository.updateState(record);
        }
    }

    /**
     * 获取代付出账方
     */
    private String getOutSupplier(PlusOrderShuntEntity orderShunt) {
        // 没有分流 或 桔子/黑卡的 走航纳出账
        if (orderShunt == null || orderShunt.getInSupplier() == null
                || orderShunt.getInSupplier() == SupplierEnum.XSHK.getCode()) {
            return "航纳";
        }
        PlusShuntSupplierEntity supplierCache = shuntRepository.getSupplierCache(
                orderShunt.getInSupplier());
        if (supplierCache != null) {
            // 兼容上线期间，未配置分流主体的情况
            return supplierCache.getSupplierName();
        }
        return SupplierEnum.getName(orderShunt.getInSupplier());
    }

    /**
     * 获取代付商编
     */
    private String getDefrayNo(PlusOrderShuntEntity orderShunt) {
        // 黑卡不需要传代付商编
        if (orderShunt == null || orderShunt.getInSupplier() == null
                || orderShunt.getInSupplier() == SupplierEnum.XSHK.getCode()) {
            return null;
        }
        PlusShuntSupplierEntity supplierCache = shuntRepository.getSupplierCache(
                orderShunt.getInSupplier());
        if (supplierCache != null && supplierCache.getPay() != null) {
            return supplierCache.getPay().getDefrayMerchantId();
        }
        // 兼容上线期间，未配置分流主体的情况
        String defrayMerchant = configProperties.defrayMerchant;
        if (StringUtils.isBlank(defrayMerchant)) {
            return null;
        }
        JSONObject json = JSONObject.parseObject(defrayMerchant);
        return json.getString(String.valueOf(orderShunt.getInSupplier()));
    }

    /**
     * 获取分流信息
     */
    private PlusOrderShuntEntity getOrderShunt(String plusOrderSn, Integer configId) {
        if (!PlusConstant.SHUNT_CARD_LIST.contains(configId)) {
            return null;
        }
        return shuntQueryModel.getByOrderSn(plusOrderSn);
    }

    /**
     * 加锁，十分钟之内不允许再次发起（可能请求支付异常）如果支付10分钟之内没有回调给打款结果，认为支付没有收到请求，可以再次发起
     */
    private void tryLock(PastMemberRefundRecordEntity record, Long expire) {
        Integer businessType = record.getBusinessType();
        String redisKey;
        if (DefrayPayBusinessTypeEnum.PAST.getCode() == businessType) {
            log.info("调用支付代付接口异常-过期取消加锁十分钟：{}", record.getOrderSn());
            redisKey = RedisConstantPrefix.PAST_CANCEL_REPEAT_ORDER + record.getOrderSn();
        } else {
            log.info("调用支付代付接口异常-代付取消加锁十分钟：{}", record.getOrderSn());
            redisKey = RedisConstantPrefix.ANOTHER_PAY_CANCEL_REPEAT_ORDER + record.getOrderSn();
        }
        redisLock.lock(redisKey, "1", expire);
    }

    /**
     * 发送代付打款结果内部消息
     */
    private void sendDefraySynResult(PastMemberRefundRecordEntity record, PlusOrderEntity plusOrder,
            BigDecimal refundAmount) {
        // 发送消息,内部消费处理
        PlusOrderDefrayResultMqEvent event = new PlusOrderDefrayResultMqEvent();
        event.setOrderInfo(plusOrder);
        event.setRecord(record);
        event.setRefundAmount(refundAmount);
        messageExternalRepository.sendDefrayResult(event);
    }

    /**
     * 通知三方出账
     */
    private void outcomeNotify(String orderSn, Integer refundState, BigDecimal refundAmount) {
        OrderOutcomeNotifyEvent event = new OrderOutcomeNotifyEvent();
        event.setPlusOrderSn(orderSn);
        event.setRefundType(CommonConstant.ONE);
        event.setRefundAmount(refundAmount);
        event.setRefundState(refundState);
        orderBillModel.outcomeNotify(event);
    }

    private void outcomeNotifyNew(String orderSn, Integer refundState, BigDecimal refundAmount, String serialNo) {
        OrderOutcomeNotifyEvent event = new OrderOutcomeNotifyEvent();
        event.setPlusOrderSn(orderSn);
        event.setRefundType(CommonConstant.ONE);
        event.setRefundAmount(refundAmount);
        event.setRefundState(refundState);
        event.setSerialNo(serialNo);
        orderBillModel.outcomeNotify(event);
    }

    /**
     * 处理退款金额为0元订单
     */
    private void handleZeroOrder(PastMemberRefundRecordEntity record, PlusOrderEntity plusOrderInfo,
            String outSupplier) {
        String orderSn = plusOrderInfo.getOrderSn();
        // 更新记录状态：5_仅取消订单未退款
        record.setOptStatus(PastCancelStatusEnum.OPT_STATE_5.getStatus());
        record.setOptResult(PastCancelStatusEnum.OPT_STATE_5.getRemark());
        repository.updateState(record);
        // 发送消息,内部消费
        sendDefraySynResult(record, plusOrderInfo, BigDecimal.ZERO);
        // 保存操作日志
        saveLog(record, plusOrderInfo,
                StringUtils.isNotBlank(record.getRemark()) ? "取消比例为0%;" + orderSn
                        : "取消比例为0%;该笔退款由" + outSupplier + "出账");
        log.info("发起打款操作-仅取消订单处理结束：tradeAmount:{}分,orderSn：{}", BigDecimal.ZERO,
                orderSn);
    }


    /**
     * 保存操作日志
     */
    private void saveLog(PastMemberRefundRecordEntity record, PlusOrderEntity plusOrder,
            String cancelRemark) {
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(plusOrder.getUserId()).channelId(plusOrder.getChannelId())
                .programId(plusOrder.getProgramId()).orderSn(plusOrder.getOrderSn())
                .cancelRemark(cancelRemark).reason(record.getCancelReason())
                .optId(record.getOperatingId()).optName(record.getOperatingName());
        logRepository.saveCancelLogByUser(plusLog,
                DefrayPayBusinessTypeEnum.PAST.getCode() == record.getBusinessType()
                        ? LogNodeEnum.LOG_NODE_PAST_REFUND : LogNodeEnum.LOG_NODE_DE_CANCEL);
    }
}

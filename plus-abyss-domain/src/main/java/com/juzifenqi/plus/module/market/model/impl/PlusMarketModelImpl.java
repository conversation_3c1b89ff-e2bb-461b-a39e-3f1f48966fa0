package com.juzifenqi.plus.module.market.model.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.enumeration.OrderStateEnum;
import com.juzifenqi.magic.bean.vo.PlusMarketDetailVO.ProgramBasicInfo;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.NewGradeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.PlusSceneCodeEnum;
import com.juzifenqi.plus.enums.PlusSmsSendNodeEnum;
import com.juzifenqi.plus.enums.PlusSwitchEnum;
import com.juzifenqi.plus.enums.discount.DiscountConfTypeEnum;
import com.juzifenqi.plus.enums.market.ShowContentEnum;
import com.juzifenqi.plus.enums.market.ShowTypeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberCouponInfoEntity;
import com.juzifenqi.plus.module.common.ICreditExternalRepository;
import com.juzifenqi.plus.module.common.IMemberSwitchControlRepository;
import com.juzifenqi.plus.module.common.IPlusOrderExternalRepository;
import com.juzifenqi.plus.module.common.ISmsRepository;
import com.juzifenqi.plus.module.common.entity.DistributionMarketEntity;
import com.juzifenqi.plus.module.common.entity.MemberPlusSwitchControlEntity;
import com.juzifenqi.plus.module.common.event.PlusSmsParamEvent;
import com.juzifenqi.plus.module.market.model.IPlusMarketModel;
import com.juzifenqi.plus.module.market.model.PlusLoanDiscountInfoEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.*;
import com.juzifenqi.plus.module.market.model.event.PlusLoanConfirmMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanMarketEvent;
import com.juzifenqi.plus.module.order.model.IResubmitGroupQueryModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusCustomerGroupRecordEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDiscountEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountRecordPo;
import com.juzifenqi.plus.module.program.model.IPlusCouponIndexQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzishuke.credit.request.CreditCalculateRequest;
import com.juzishuke.credit.vo.CreditCalculateVO;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 营销model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/11 18:22
 */
@Slf4j
@Service
public class PlusMarketModelImpl implements IPlusMarketModel {

    @Autowired
    private PlusOrderQueryModel            orderQueryModel;
    @Autowired
    private IPlusProgramQueryModel         queryModel;
    @Autowired
    private IPlusCouponIndexQueryModel     plusCouponIndexQueryModel;
    @Autowired
    private ICreditExternalRepository      creditExternalRepository;
    @Autowired
    private IPlusOrderExternalRepository   orderExternalRepository;
    @Autowired
    private IOrderExternalRepository       orderRepository;
    @Autowired
    private IMemberSwitchControlRepository controlRepository;
    @Autowired
    private IResubmitGroupQueryModel       groupQueryModel;
    @Autowired
    private RedisUtils                     redisUtils;
    @Autowired
    private ISmsRepository                 smsRepository;
    @Autowired
    private IPlusProModelRepository plusProModelRepository;

    @Override
    public PlusLoanConfirmInfoEntity handleLoanConfirmInfo(PlusLoanConfirmMarketEvent event) {
        log.info("处理确认借款页信息开始：{}", event.getUserId());
        PlusLoanConfirmInfoEntity entity = new PlusLoanConfirmInfoEntity();
        // 构建返回结果
        List<PlusOrderEntity> sevenPlusOrder = orderQueryModel.getSevenPlusOrder(event.getUserId(),
                event.getChannelId());
        entity.setSevenPlusOrder(sevenPlusOrder);
        // 7天内是否买过提额卡
        boolean hasTe = !CollectionUtils.isEmpty(sevenPlusOrder) && sevenPlusOrder.stream()
                .anyMatch(e -> e.getConfigId() == JuziPlusEnum.NEW_JUXP_CARD.getCode()
                        || e.getConfigId() == JuziPlusEnum.QUOTA_CARD.getCode()
                        || e.getConfigId() == JuziPlusEnum.YITONG_CARD.getCode()
                        || e.getConfigId() == JuziPlusEnum.HYYK_CARD.getCode()
                        || e.getConfigId() == JuziPlusEnum.SUCCESS_CARD.getCode());
        if (hasTe) {
            entity.setSevenQuotaPlusOrder(true);
            // 处理取消标识
            PlusOrderEntity order = sevenPlusOrder.stream()
                    .filter(e -> e.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()
                            && Objects.equals(e.getPayType(),
                            PlusOrderPayTypeEnum.PAY_AFTER.getValue())).findFirst().orElse(null);
            if (Objects.nonNull(order) && JuziPlusEnum.HYYK_CARD.getCode() != order.getConfigId()) {
                entity.setWaitPayOrder(order);
            }
        }
        return entity;
    }

    @Override
    public PlusLoanInfoEntity handleLoanInfo(PlusLoanMarketEvent event,
            DistributionMarketEntity disResult, Orders order) {
        Integer userId = event.getUserId();
        log.info("处理借款首页信息开始：{}", userId);
        PlusLoanInfoEntity entity = new PlusLoanInfoEntity();
        try {
            // 构建返回结果
            List<PlusOrderEntity> sevenOrders = orderQueryModel.getSevenPlusOrder(event.getUserId(),
                    event.getChannelId());
            entity.setSevenPlusOrder(sevenOrders);
            if (!CollectionUtils.isEmpty(sevenOrders) && sevenOrders.stream().anyMatch(
                    e -> e.getConfigId() == JuziPlusEnum.NEW_JUXP_CARD.getCode()
                            || e.getConfigId() == JuziPlusEnum.QUOTA_CARD.getCode()
                            || e.getConfigId() == JuziPlusEnum.YITONG_CARD.getCode()
                            || e.getConfigId() == JuziPlusEnum.HYYK_CARD.getCode()
                            || e.getConfigId() == JuziPlusEnum.EXPEDITE_CARD.getCode())) {
                log.info("开始处理借款首页7天内买过卡重提客群展示：{}", userId);
                if (order != null && Objects.equals(order.getOrderState(),
                        OrderStateEnum.备货中.getState())) {
                    PlusCustomerGroupRecordEntity groupRecord = groupQueryModel.getByOrderSn(
                            order.getOrderSn());
                    log.info("借款首页7天内买过卡获取重提客群记录：{}", JSON.toJSON(groupRecord));
                    if (groupRecord != null) {
                        // 是重提客群
                        entity.setResubmitCustomerGroupFlag(1);
                        // 是否点击过：确认调整 按钮
                        entity.setReduceQuotaFlag(groupRecord.getReduceQuotaButton());
                        // 是否点击过：放弃借款 按钮
                        entity.setWaiveLoanFlag(groupRecord.getWaiveLoanButton());
                        // 订单号
                        entity.setOrderSn(order.getOrderSn());
                        // 20240329 zjf 重提客群迭代：返回对应重提客群金额、期数、资方id
                        entity.setResubmitGroupOrderAmount(groupRecord.getOrderAmount());
                        entity.setResubmitGroupOrderPeriods(groupRecord.getPeriods());
                        // 20240329 zjf 重提客群迭代：获取被拒订单的资方id，如果查询异常/返回为空则默认给前端自营资方code试算
                        Orders rejectOrder = orderRepository.getOrdersBySn(
                                groupRecord.getRejectOrderSn());
                        entity.setCapitalId(
                                rejectOrder != null && rejectOrder.getCapitalId() != null
                                        ? rejectOrder.getCapitalId() : 0);
                    }
                }
                if (entity.getResubmitCustomerGroupFlag() == 1
                        && disResult.getResubmitConfirm() == 1) {
                    log.info("7天内买过桔享、固额、加速卡，是重提客群需要二次弹窗，且是二次弹窗：{}",
                            userId);
                    Optional<PlusOrderEntity> maxTimeOrder = sevenOrders.stream()
                            .filter(e -> e.getConfigId() == JuziPlusEnum.NEW_JUXP_CARD.getCode()
                                    || e.getConfigId() == JuziPlusEnum.QUOTA_CARD.getCode()
                                    || e.getConfigId() == JuziPlusEnum.YITONG_CARD.getCode()
                                    || e.getConfigId() == JuziPlusEnum.HYYK_CARD.getCode()
                                    || e.getConfigId() == JuziPlusEnum.EXPEDITE_CARD.getCode())
                            .max(Comparator.comparing(PlusOrderEntity::getCreateTime));
                    maxTimeOrder.ifPresent(orderInfo -> entity.setSevenOpenOrderAmount(
                            orderInfo.getOrderAmount()));
                }
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "处理借款首页信息异常");
        }
        return entity;
    }

    @Override
    public PlusLoanInfoEntity handleLoanMarketInfo(PlusLoanMarketEvent event, Orders order) {
        Integer userId = event.getUserId();
        PlusLoanInfoEntity entity = new PlusLoanInfoEntity();
        if (order == null || !Objects.equals(order.getOrderState(),
                OrderStateEnum.备货中.getState())) {
            log.info("加速卡营销-重提营销客群校验，订单状态为501，不营销加速卡：{}", userId);
            return entity;
        }
        PlusCustomerGroupRecordEntity groupRecord = groupQueryModel.getByOrderSn(
                order.getOrderSn());
        if (groupRecord == null) {
            log.info(
                    "借款首页加速卡营销-重提营销客群校验，查询重提客群记录为空，非重提客群，不影响原流程：{}",
                    userId);
            return entity;
        }
        // 是重提客群
        entity.setResubmitCustomerGroupFlag(CommonConstant.ONE);
        // 是否点击过：确认调整 按钮
        entity.setReduceQuotaFlag(groupRecord.getReduceQuotaButton());
        // 是否点击过：放弃借款 按钮
        entity.setWaiveLoanFlag(groupRecord.getWaiveLoanButton());
        // 20240329 zjf 重提客群迭代：返回对应重提客群金额、期数、资方id
        entity.setResubmitGroupOrderAmount(groupRecord.getOrderAmount());
        entity.setResubmitGroupOrderPeriods(groupRecord.getPeriods());
        // 20240329 zjf 重提客群迭代：获取被拒订单的资方id，如果查询异常/返回为空则默认给前端自营资方code试算
        Orders rejectOrder = orderRepository.getOrdersBySn(groupRecord.getRejectOrderSn());
        entity.setCapitalId(rejectOrder != null && rejectOrder.getCapitalId() != null
                ? rejectOrder.getCapitalId() : 0);
        return entity;
    }

    @Override
    public Integer getDelayDeductTime(Integer configId) {
        log.info("获取延迟划扣时间入参：{}", configId);
        Integer switchCode = null;
        if (configId.equals(JuziPlusEnum.QUOTA_CARD.getCode())) {
            switchCode = PlusSwitchEnum.FIXED_QUOTA_DEDUCT.getCode();
        } else if (configId.equals(JuziPlusEnum.NEW_JUXP_CARD.getCode())) {
            switchCode = PlusSwitchEnum.ENJOY_CARD_DEDUCT.getCode();
        } else if (configId.equals(JuziPlusEnum.SUCCESS_CARD.getCode())) {
            switchCode = PlusSwitchEnum.NEW_PERSON_CARD_DEDUCT.getCode();
        } else if (configId.equals(JuziPlusEnum.EXPEDITE_CARD.getCode())) {
            switchCode = PlusSwitchEnum.EXPEDITE_PlUS_DEDUCT.getCode();
        } else if (configId.equals(JuziPlusEnum.DOWNRATE_CARD.getCode())) {
            switchCode = PlusSwitchEnum.PlUS_AUTOMATIC_DEDUCT.getCode();
        } else if (configId.equals(JuziPlusEnum.YITONG_CARD.getCode())) {
            switchCode = PlusSwitchEnum.YT_DEDUCT.getCode();
        } else if (configId.equals(JuziPlusEnum.HYYK_CARD.getCode())) {
            switchCode = PlusSwitchEnum.YK_DEDUCT.getCode();
        }
        if (Objects.isNull(switchCode)) {
            log.info("获取延迟划扣时间,开关code为空：{}", configId);
            return null;
        }
        MemberPlusSwitchControlEntity switchControl = controlRepository.getSwitchByCode(switchCode);
        log.info("获取会员开关控制表数据信息：{}", JSON.toJSON(switchControl));
        if (switchControl == null || switchControl.getStatus() == null
                || switchControl.getStatus() == 2 || switchControl.getDeductType() == null
                || switchControl.getDeductType() == 1) {
            log.info("会员划扣开关未开启或退款方式不是延时退款：{}", configId);
            return null;
        }
        return switchControl.getDeductDelay();
    }

    @Override
    public Integer getShowContent(Integer programId, Integer configId, String flag) {
        log.info("获取营销展示内容开始：{},{}", configId, flag);
        // 新人卡提额
        if (JuziPlusEnum.SUCCESS_CARD.getCode() == configId) {
            return ShowContentEnum.SUC_QUOTA.getCode();
        }
        if (JuziPlusEnum.EXPEDITE_CARD.getCode() == configId) {
            return ShowContentEnum.QUOTA.getCode();
        }
        if (JuziPlusEnum.QUOTA_CARD.getCode() == configId
                || JuziPlusEnum.NEW_JUXP_CARD.getCode() == configId
                || JuziPlusEnum.HYYK_CARD.getCode() == configId
                || JuziPlusEnum.YITONG_CARD.getCode() == configId) {
            int quotaCount = queryModel.countProModelByProgramId(programId,
                    PlusModelEnum.HYTE.getModelId());
            return quotaCount > 0 ? ShowContentEnum.QUOTA.getCode()
                    : ShowContentEnum.NO_QUOTA.getCode();
        }
        return ShowContentEnum.OPEN.getCode();
    }

    @Override
    public BigDecimal getRepayCouponAmount(Integer programId) {
        List<MemberCouponInfoEntity> couponList = plusCouponIndexQueryModel.getKklCouponByProgramId(
                programId);
        if (CollectionUtils.isEmpty(couponList)) {
            return BigDecimal.ZERO;
        }
        return couponList.stream().filter(e -> e.getCouponCategory() == CommonConstant.THREE)
                .map(e -> new BigDecimal(e.getPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public CreditCalculateVO creditCalculateSimulation(Integer userId, Integer programId,
            BigDecimal riskAmount) {
        if (riskAmount == null) {
            log.info("试算额度风控可提金额为空,无法试算：{},{}", userId, programId);
            return null;
        }
        // 获取营销的提额等级
        PlusLiftAmountEntity liftAmount = queryModel.getByProgramId(programId);
        if (liftAmount == null) {
            log.info("试算额度未获取到方案提额等级,无法试算：{},{}", userId, programId);
            return null;
        }
        CreditCalculateRequest request = new CreditCalculateRequest();
        request.setCustomerId(userId);
        request.setQuota(riskAmount);
        request.setBizType(3);
        request.setItemCode(NewGradeEnum.getNewGrade(liftAmount.getGrade()));
        return creditExternalRepository.creditCalculateSimulation(request);
    }

    @Override
    public PlusDiscountEntity getDiscount(Integer userId, Integer channelId, Integer configId,
            Integer sceneCode, DistributionMarketEntity market) {
        // 借款首页-加速卡走新折扣
        if (configId == JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            log.info("借款首页加速卡营销新折扣开始：{},{}", userId, channelId);
            return orderExternalRepository.getNewDiscount(userId, channelId, configId, market);
        }
        return orderExternalRepository.getDiscount(userId, channelId, configId, sceneCode);
    }

    @Override
    public PlusLoanDiscountInfoEntity handleLoanDiscountInfo(PlusLoanMarketEvent event,
            ProgramBasicInfo program, PlusDiscountEntity discount, Orders order,
            PlusLoanMarketEntity result) {
        PlusLoanDiscountInfoEntity discountInfoEntity = new PlusLoanDiscountInfoEntity();
        Integer configId = program.getConfigId();
        Integer userId = event.getUserId();
        if (configId != JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            log.info("处理借款首页新折扣弹窗及短信,非加速卡,不处理：{},{}", userId, configId);
            return discountInfoEntity;
        }
        Integer channelId = event.getChannelId();
        log.info("处理借款首页新折扣弹窗及短信开始：{}", userId);
        // 是否需要弹窗
        String redisKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.MEMBER_DISCOUNT_ALERT,
                userId, configId);
        if (!redisUtils.hasKey(redisKey)) {
            discountInfoEntity.setNeedDiscountAlert(true);
            redisUtils.setEx(redisKey, "1", 24, TimeUnit.HOURS);
            log.info("新折扣弹窗缓存设置完成：{}", userId);
            // 每个用户只发放一次折扣营销短信
            List<MemberPlusDiscountRecordPo> userRecords = orderExternalRepository.getDiscountRecord(
                    userId, channelId, configId, DiscountConfTypeEnum.PLUS.getCode());
            if (!CollectionUtils.isEmpty(userRecords) && userRecords.size() == 1) {
                log.info("发送新折扣营销短信开始：{}", userId);
                PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
                plusSmsParamVo.setUserId(userId);
                plusSmsParamVo.setConfigId(configId);
                plusSmsParamVo.setChannelId(channelId);
                plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_19.getCode());
                plusSmsParamVo.setLoanOrderSn(order != null ? order.getOrderSn() : null);
                plusSmsParamVo.setProgramId(program.getProgramId());
                PlusProgramEntity plusProgram = queryModel.getById(program.getProgramId());
                if (plusProgram != null) {
                    plusSmsParamVo.setSubtractAmount(
                            plusProgram.getMallMobilePrice().subtract(result.getDiscountPrice()));
                }
                smsRepository.sendSmsByConfig(plusSmsParamVo);
            }
        }
        log.info("处理借款首页新折扣弹窗及短信完成：{}", userId);
        return discountInfoEntity;
    }

    @Override
    public List<LoanSuccessOrderEntity> getLoanSuccessOrder(Integer configId) {
        if (configId != JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            return null;
        }
        return orderQueryModel.getLoanSuccessOrder();
    }

    @Override
    public MarketingCardEntity getMarketingCardEntity(ProgramBasicInfo basicInfo) {
        log.info("获取借款首页其他卡营销内容开始：{}", basicInfo.getProgramId());
        MarketingCardEntity entity = new MarketingCardEntity();
        entity.setShowType(ShowTypeEnum.getShowTypeByConfigId(basicInfo.getConfigId()));
        entity.setProgramId(basicInfo.getProgramId());
        entity.setMallMobilePrice(basicInfo.getMallMobilePrice());
        return entity;
    }

    @Override
    public List<PlusProModelEntity> getPlusModelList(Integer programId) {
        log.info("获取权益列表的方案id programId：{}", programId);
        return plusProModelRepository.getPlusProModelList(programId);
    }
}

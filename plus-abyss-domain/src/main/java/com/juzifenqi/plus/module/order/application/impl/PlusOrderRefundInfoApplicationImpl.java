package com.juzifenqi.plus.module.order.application.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.RefundInfo;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PaySourceConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.CancelReasonEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PayRefundTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.RefundStateEnum;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.enums.refund.RefundInfoStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.repository.external.acl.feishu.FeiShuModel;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderBillApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderRefundInfoApplication;
import com.juzifenqi.plus.module.order.application.converter.IPlusOrderRefundInfoApplicationConverter;
import com.juzifenqi.plus.module.order.model.*;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.repeat.MemberPlusRepeatOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IMessageExternalRepository;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.CancelRenewalPlanEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderCancelExtInfoEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderOutcomeNotifyEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/7 14:30
 */
@Slf4j
@Service
public class PlusOrderRefundInfoApplicationImpl implements IPlusOrderRefundInfoApplication {

    /**
     * 分割线
     */
    private static final String SPLIT_LINE = "• ";

    IPlusOrderRefundInfoApplicationConverter converter = IPlusOrderRefundInfoApplicationConverter.instance;

    @Autowired
    private IPlusOrderRefundInfoModel orderRefundInfoModel;
    @Autowired
    private PlusOrderQueryModel       plusOrderQueryModel;
    @Autowired
    private IIMRepository             imRepository;
    @Autowired
    private RedissonClient            redissonClient;
    @Autowired
    private IPlusOrderApplication     orderApplication;
    @Autowired
    private IPlusOrderBillApplication orderBillApplication;
    @Autowired
    private IPlusRepeatOrderModel     repeatOrderModel;
    @Autowired
    private IMessageExternalRepository messageExternalRepository;
    @Autowired
    private IOrderExternalRepository orderExternalRepository;
    @Autowired
    private IPlusShuntRepository shuntRepository;
    @Autowired
    private IPlusOrderSeparateRepository separateRepository;
    @Autowired
    private PlusOrderSeparateModel plusOrderSeparateModel;
    @Autowired
    private PlusOrderModel plusOrderModel;
    @Autowired
    private IPlusMonthMemberRenewalPlanModel plusMonthMemberRenewalPlanModel;
    @Resource
    private ConfigProperties  configProperties;

    @Resource
    private FeiShuModel feiShuModel;

    @Override
    public void originalRefund(OrderRefundNotifyEntity entity) {
        String thirdPayNum = entity.getThirdPayNum();
        log.info("原路退款、原路换卡退款处理开始,{}", thirdPayNum);
        // 校验订单退款信息
        PlusOrderRefundInfoEntity refundInfo = orderRefundInfoModel.getByRefundSerialNo(thirdPayNum);
        PlusOrderRefundDetailEntity refundDetailEntity = orderRefundInfoModel.getDetailByRefundSerialNo(thirdPayNum);
        if (refundDetailEntity != null) {
            firstPayOriginalRefund(entity, refundDetailEntity);
            return;
        }
        if (refundInfo == null) {
            log.info("处理订单中心退款通知,订单退款信息不存在,{}", thirdPayNum);
            imRepository.sendImMessage(
                    "处理订单中心退款通知,订单退款信息不存在,退款业务流水号:" + thirdPayNum);
            return;
        }
        if (!Objects.equals(refundInfo.getRefundState(), RefundInfoStateEnum.DOING.getCode())) {
            log.info("处理订单中心退款通知,订单退款信息非处理中,不处理,{}", thirdPayNum);
            return;
        }
        // 校验会员订单信息
        String plusOrderSn = refundInfo.getOrderSn();
        PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(plusOrderSn);
        if (plusOrderInfo == null) {
            log.info("处理订单中心退款通知,未查到订单信息,{}", plusOrderSn);
            return;
        }
        Integer orderState = plusOrderInfo.getOrderState();
        if (orderState == PlusOrderStateEnum.CANCELED.getCode()) {
            log.info("处理订单中心退款通知,订单状态已经取消,{}", thirdPayNum);
            PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
            updateRefundInfo.setId(refundInfo.getId());
            updateRefundInfo.setRefundState(RefundInfoStateEnum.SUCCESS.getCode());
            updateRefundInfo.setPaySerialNo(entity.getSerialNumber());
            orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);
            return;
        }
        // 更新订单退款信息
        String status = entity.getStatus();
        Integer refundState =
                RefundStateEnum.S.getCode().equals(status) ? RefundInfoStateEnum.SUCCESS.getCode()
                        : RefundInfoStateEnum.FAIL.getCode();
        PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
        updateRefundInfo.setId(refundInfo.getId());
        updateRefundInfo.setRefundState(refundState);
        updateRefundInfo.setPaySerialNo(entity.getSerialNumber());
        updateRefundInfo.setPayCallbackTime(new Date());
        orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);
        // 退款成功,按用户id维度加锁,防止取消订单挪动会员周期出现错误
        if (RefundStateEnum.S.getCode().equals(status)) {
            // 重复支付的单子处理
            MemberPlusRepeatOrderEntity repeatOrder = repeatOrderModel.getByOrderSn(plusOrderSn);
            if (repeatOrder != null) {
                log.info("新退款回调处理重复支付订单取消开始：{}", plusOrderSn);
                repeatOrderModel.cancel(repeatOrder.getId());
                // 取消订单
                PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(refundInfo,
                        entity.getRefundAmount(), true, null);
                PlusOrderCancelEntity cancelEntity = new PlusOrderCancelEntity();
                cancelEntity.setMoneyBack(repeatOrder.getOrderAmount());
                orderApplication.cancelRepOrder(cancelEvent, cancelEntity);
            } else {
                log.info("新退款回调处理正常订单取消开始：{}", plusOrderSn);
                OrderCancelExtInfoEvent extInfo = JSONObject.parseObject(entity.getExtInfo(),
                        OrderCancelExtInfoEvent.class);
                PlusOrderCancelEvent event = converter.toPlusOrderCancelEvent(refundInfo,
                        entity.getRefundAmount(), true, extInfo);
                handleRefundSuccess(entity.getThirdPayNum(), plusOrderInfo.getUserId(), event);
            }
            // 作废会员月卡续费计划
            if (JuziPlusEnum.HYYK_CARD.getCode() == plusOrderInfo.getConfigId()
                    && plusOrderInfo.getMonthPeriod() == 1) {
                plusMonthMemberRenewalPlanModel.cancelRenewalPlan(plusOrderSn);
            }
        }
        // 通知黑卡出账
        OrderOutcomeNotifyEvent notifyEvent = new OrderOutcomeNotifyEvent();
        notifyEvent.setRefundState(RefundStateEnum.S.getCode().equals(status) ? 1 : 2);
        notifyEvent.setPlusOrderSn(plusOrderSn);
        notifyEvent.setRefundType(2);
        notifyEvent.setRefundAmount(entity.getRefundAmount());
        orderBillApplication.outcomeNotify(notifyEvent);
    }

    @Override
    public void secondRefund(OrderRefundSecondEvent event) {
        PlusOrderRefundDetailEntity detailEntity = orderRefundInfoModel.getDetailById(event.getRefundDetailId());
        if (!Objects.equals(detailEntity.getRefundState(), RefundInfoStateEnum.INIT.getCode())) {
            log.error("refundDetailId:{}, 状态异常", event.getRefundDetailId());
            return;
        }
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(event.getOrderSn());
        PlusOrderRefundInfoEntity refundInfoEntity = orderRefundInfoModel.getById(detailEntity.getRefundInfoId());
        PlusOrderRefundDetailEntity updateDetailEntity = new PlusOrderRefundDetailEntity();
        updateDetailEntity.setId(detailEntity.getId());
        updateDetailEntity.setRefundState(RefundInfoStateEnum.DOING.getCode());
        orderRefundInfoModel.updateOrderRefundDetail(updateDetailEntity);
        OrderCancelRefundVO refundVO = buildOrderCancelParam(plusOrderEntity, refundInfoEntity, detailEntity);
        OrderCancelRefundResultVO orderCancelRefundResultVO = orderExternalRepository.closeOrderRefund(refundVO);
        if (orderCancelRefundResultVO != null
                && StringUtils.isNotBlank(orderCancelRefundResultVO.getSerialNumber())) {
            orderRefundInfoModel.updateRefundDetailBySerialNo( detailEntity.getRefundSerialNo() ,orderCancelRefundResultVO.getSerialNumber());
        }
    }


    /**
     * 构建调订单中心取消订单参数
     */
    private OrderCancelRefundVO buildOrderCancelParam(PlusOrderEntity order, PlusOrderRefundInfoEntity refundEntity,
                                                      PlusOrderRefundDetailEntity refundDetailEntity) {
        OrderCancelRefundVO vo = new OrderCancelRefundVO();
        vo.setOrderSn(order.getOrderSn());
        vo.setCancelReason(CancelReasonEnum.getNameByCode(refundEntity.getCancelReason()));
        vo.setOperatingId(refundEntity.getOptUserId());
        vo.setOperatingName(refundEntity.getOptUserName());
        // 退款信息
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setRefundType(PayRefundTypeEnum.ORIGINAL.getCode());

        refundInfo.setPartRefund(1);
        refundInfo.setApplication(String.valueOf(order.getChannelId()));
        refundInfo.setSource(PaySourceConstant.PAY_SOURCE_MEMBER);
        refundInfo.setRefundAmount(refundDetailEntity.getRefundAmount());
        refundInfo.setThirdPayNum(refundDetailEntity.getRefundSerialNo());
        refundInfo.setPayProductCode(PayProductCodeEnum.TK.getCode());

        PlusOrderSeparateEntity separateEntity = plusOrderSeparateModel.getById(refundDetailEntity.getSeparateId());
        String payNum = Objects.equals(separateEntity.getOrderPayAction(), 1) ?
                separateEntity.getSerialNo() : separateEntity.getPaySerialNo();
        refundInfo.setCancelPeriod(refundDetailEntity.getTotalPeriod());
        refundInfo.setRefundTotalAmount(refundDetailEntity.getTotalRefundAmount());
        refundInfo.setPayNum(payNum);

        // 交易摘要
        refundInfo.setTradeSubject(CommonConstant.SUBJECT);
        // 业务场景
        refundInfo.setBusinessScene(separateEntity.getBusinessScene());

        vo.setRefundInfo(refundInfo);
        return vo;
    }


    private void firstPayOriginalRefund(OrderRefundNotifyEntity entity, PlusOrderRefundDetailEntity refundDetailEntity) {
        if (!Objects.equals(refundDetailEntity.getRefundState(), RefundInfoStateEnum.DOING.getCode())) {
            log.info("处理订单中心退款通知,订单退款详情非处理中,不处理,{}", refundDetailEntity.getRefundSerialNo());
            return;
        }
        // 校验会员订单信息
        PlusOrderRefundInfoEntity refundInfo = orderRefundInfoModel.getById(refundDetailEntity.getRefundInfoId());
        if (refundInfo == null) {
            log.error("未查询到退款订单记录,refundInfoId={}", refundDetailEntity.getRefundInfoId());
            return;
        }
        String orderSn = refundInfo.getOrderSn();
        PlusOrderEntity plusOrderInfo = plusOrderQueryModel.getByOrderSn(orderSn);
        if (plusOrderInfo == null) {
            log.info("处理订单中心退款通知,未查到订单信息,{}", orderSn);
            return;
        }
        // 更新订单退款信息
        String status = entity.getStatus();
        Integer refundState =
                RefundStateEnum.S.getCode().equals(status) ? RefundInfoStateEnum.SUCCESS.getCode()
                        : RefundInfoStateEnum.FAIL.getCode();
        PlusOrderRefundDetailEntity updateDetailEntity = new PlusOrderRefundDetailEntity();
        updateDetailEntity.setId(refundDetailEntity.getId());
        updateDetailEntity.setRefundState(refundState);
        updateDetailEntity.setRefundSerialNo(refundInfo.getRefundSerialNo());
        updateDetailEntity.setPayCallbackTime(new Date());
        orderRefundInfoModel.updateOrderRefundDetail(updateDetailEntity);
        List<PlusOrderRefundDetailEntity> detailEntityList = orderRefundInfoModel.getDetailsByInfoId(refundInfo.getId());
        // 退款成功,按用户id维度加锁,防止取消订单挪动会员周期出现错误
        if (RefundStateEnum.S.getCode().equals(status)) {
            if (Objects.equals(refundDetailEntity.getCurrentPeriod(), refundDetailEntity.getTotalPeriod())) {
                PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
                updateRefundInfo.setId(refundInfo.getId());
                updateRefundInfo.setRefundState(refundState);
                updateRefundInfo.setPayCallbackTime(new Date());
                orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);

                OrderCancelExtInfoEvent extInfo = JSONObject.parseObject(entity.getExtInfo(),
                        OrderCancelExtInfoEvent.class);
                PlusOrderCancelEvent event = converter.toPlusOrderCancelEvent(refundInfo,
                        refundDetailEntity.getTotalRefundAmount(), true, extInfo);
                handleRefundSuccess(entity.getThirdPayNum(), plusOrderInfo.getUserId(), event);
            } else {
                //更新订单退款金额
                plusOrderModel.updateOrderRefundAmount(plusOrderInfo.getOrderSn(),refundDetailEntity.getRefundAmount());
                //发起下一期退款
                Optional<PlusOrderRefundDetailEntity> next = detailEntityList.stream().filter(
                        t -> Objects.equals(t.getCurrentPeriod(), refundDetailEntity.getTotalPeriod()) &&
                                Objects.equals(t.getRefundState(), RefundInfoStateEnum.INIT.getCode())).findFirst();
                if (next.isPresent()) {
                    PlusOrderRefundDetailEntity e = next.get();
                    OrderRefundSecondEvent event = new OrderRefundSecondEvent();
                    event.setRefundType(PayRefundTypeEnum.ORIGINAL.getCode());
                    event.setOrderSn(orderSn);
                    event.setRefundDetailId(e.getId());
                    messageExternalRepository.sendSecondRefundMq(event);
                }
            }
        } else if (RefundStateEnum.F.getCode().equals(status)) {
            if (refundDetailEntity.getCurrentPeriod() < refundDetailEntity.getTotalPeriod()) {
                PlusOrderRefundDetailEntity nextRefundDetail = detailEntityList.stream().filter(
                                t -> Objects.equals(t.getRefundState(), RefundInfoStateEnum.INIT.getCode())
                                        && Objects.equals(t.getCurrentPeriod(), t.getTotalPeriod()))
                        .findFirst().orElse(null);
                if (Objects.nonNull(nextRefundDetail)) {
                    //更新记录为失败
                    PlusOrderRefundDetailEntity updateDetail = new PlusOrderRefundDetailEntity();
                    updateDetail.setId(nextRefundDetail.getId());
                    updateDetail.setRefundState(RefundInfoStateEnum.FAIL.getCode());
                    updateDetail.setRemark("首笔失败");
                    orderRefundInfoModel.updateOrderRefundDetail(updateDetail);
                }
                PlusOrderRefundInfoEntity updateRefundInfo = new PlusOrderRefundInfoEntity();
                updateRefundInfo.setId(refundInfo.getId());
                updateRefundInfo.setRefundState(refundState);
                updateRefundInfo.setPayCallbackTime(new Date());
                orderRefundInfoModel.updateOrderRefundInfo(updateRefundInfo);
            } else {
                log.error("orderSn:{}, 原路退款第二笔失败, detailId:{}", orderSn, refundDetailEntity.getId());
                feiShuModel.sendTextMsg(String.format("orderSn:%s, 第二笔退款失败", orderSn));
            }
        }
        PlusOrderSeparateEntity separateEntity = separateRepository.getById(refundDetailEntity.getSeparateId());
        // 通知出帐
        OrderOutcomeNotifyEvent notifyEvent = new OrderOutcomeNotifyEvent();
        notifyEvent.setRefundState(RefundStateEnum.S.getCode().equals(status) ? 1 : 2);
        notifyEvent.setPlusOrderSn(plusOrderInfo.getOrderSn());
        notifyEvent.setRefundType(2);
        notifyEvent.setRefundAmount(entity.getRefundAmount());
        notifyEvent.setSerialNo(separateEntity.getPaySerialNo());
        orderBillApplication.outcomeNotify(notifyEvent);
    }

    /**
     * 会员退款监控
     */
    @Override
    public Boolean refundPlusOrderMonitor(Integer lastId) {

        String lockKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.PLUS_CANCEL_ORDER_MONITOR_JOB);
        RLock rLock = redissonClient.getLock(lockKey);
        try{
            boolean flag = rLock.tryLock();
            if (flag){
                log.info("会员退款监控任务开始");
                //获取配置
                Integer monitorTime= configProperties.refundMonitorTimeout;
                int hour = Math.negateExact(monitorTime);
                LocalDateTime now = LocalDateTime.now();
                //昨天
                LocalDateTime lastWeek = now.plusDays(-1);

                LocalDateTime dateTime = now.plusHours(hour);
                List<PlusOrderRefundInfoEntity> list = orderRefundInfoModel.getRefundInfoByStatus(RefundInfoStateEnum.DOING.getCode(), dateTime,lastWeek, lastId,100);
                List<String> orderSnList = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(list)){
                    StringBuilder sb = new StringBuilder();
                    sb.append("\uD83D\uDEA8 会员退款超时告警 \n");
                    sb.append("会员退款超时，会员订单如下: \n");
                    for(PlusOrderRefundInfoEntity refundInfo : list){
                        String orderSn = refundInfo.getOrderSn();
                        boolean exists = redissonClient.getBucket(RedisConstantPrefix.PLUS_CANCEL_ORDER_MONITOR_ORDER + orderSn)
                                .isExists();
                        if(!exists){
                            orderSnList.add(orderSn);
                        }
                    }
                    log.info("会员退款监控任务 orderSnList:{}",orderSnList);
                    if(CollectionUtil.isNotEmpty(orderSnList)){
                        List<PlusOrderEntity> plusOrderList = orderRefundInfoModel.getPlusOrderList(orderSnList);
                        log.info("会员退款监控任务 plusOrderList:{}",plusOrderList);
                        Set<String> orderInfoSet = new HashSet<>();
                        if(CollectionUtil.isNotEmpty(plusOrderList)){
                            orderInfoSet = plusOrderList.stream().map(PlusOrderEntity::getOrderSn).collect(Collectors.toSet());
                            log.info("会员退款监控任务 orderInfoSet:{}",orderInfoSet);
                        }

                        for (String order : orderSnList) {
                            if(orderInfoSet.contains(order)){
                                sb.append(SPLIT_LINE).append(order).append("\n");
                            }
                        }
                        String msg = sb.toString();
                        if(msg.contains(SPLIT_LINE)){
                            feiShuModel.sendBizAlertMsg(sb.toString(), configProperties.feishuUrl, null);
                        }
                        // 缓存订单号,12小时过期
                        for (String orderSn : orderSnList) {
                            redissonClient.getBucket(RedisConstantPrefix.PLUS_CANCEL_ORDER_MONITOR_ORDER + orderSn)
                                    .set(orderSn, 12, TimeUnit.HOURS);
                        }
                    }
                }
            }else{
                log.info("会员退款监控已有任务在处理");
            }
        }finally {
            if(rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        return true;
    }

    /**
     * 处理退款成功
     */
    private void handleRefundSuccess(String thirdPayNum, Integer userId,
            PlusOrderCancelEvent event) {
        String lockKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.PLUS_CANCEL_ORDER,
                userId);
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            boolean result = rLock.tryLock(3, TimeUnit.SECONDS);
            if (!result) {
                throw new RuntimeException("处理订单中心退款通知,加锁失败");
            }
            // 取消订单
            orderApplication.cancelPlusOrder(event);
        } catch (Exception e) {
            log.info("处理订单中心退款通知异常", e);
            if (!(e instanceof PlusAbyssException)) {
                imRepository.sendImMessage("处理订单中心退款通知异常,用户id:" + userId + ",退款业务流水号:"
                        + thirdPayNum);
            }
            throw new RuntimeException(e);
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

}
